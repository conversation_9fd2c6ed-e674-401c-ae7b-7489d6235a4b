import {HotelBookingService} from "../src";


describe('HotelBookingService', () => {
    let service: HotelBookingService;

    beforeEach(() => {
        service = new HotelBookingService();
    });

    it('test 1', () => {
        const result = service.bookRoom('single', '<PERSON>');
        expect(result).toHaveProperty('reserved', true);
        expect(result?.guestName).toBe('<PERSON>');
    });

    it('test 2', () => {
        service.bookRoom('single', '<PERSON>');
        const result = service.bookRoom('single', '<PERSON>');
        expect(result).toBeNull();
    });

    it('test 3', () => {
        const bookedRoom = service.bookRoom('single', '<PERSON>');
        if (bookedRoom) {
            const cancelledRoom = service.cancelBooking(bookedRoom.roomNumber);
            expect(cancelledRoom).toHaveProperty('reserved', false);
            expect(cancelledRoom?.guestName).toBeUndefined();
        }
    });

    it('test 4', () => {
        const result = service.cancelBooking(999);
        expect(result).toBeNull();
    });

    it('test 5', () => {
        const result = service.listAvailableRooms();
        expect(result).toHaveLength(3);
    });

    it('test 6', () => {
        const result = service.getRoomDetails(101);
        expect(result).toHaveProperty('roomNumber', 101);
    });

    it('test 7', () => {
        const result = service.getRoomDetails(999);
        expect(result).toBeNull();
    });

   });
