type RoomType = 'single' | 'double' | 'suite';


interface Room {
    roomNumber: number;
    type: RoomType;
    reserved: boolean;
    guestName?: string;
}


export class HotelBookingService {

    private rooms: Room[] = [
        {roomNumber: 101, type: 'single', reserved: false},
        {roomNumber: 102, type: 'double', reserved: false},
        {roomNumber: 103, type: 'suite', reserved: false},
        // ... more rooms here
    ];


    public bookRoom(desiredType: RoomType, guestName: string): Room | null {
        const availableRooms = this.rooms.filter(room => room.type === desiredType);
        const roomToBook = availableRooms.find(room => !room.reserved);

        roomToBook.reserved = true;
        roomToBook.guestName = guestName;

        return roomToBook;

    }

    public cancelBooking(roomNumber: number): Room | null {
        const roomToCancel = this.rooms.find(room => room.roomNumber === roomNumber);

        if (!roomToCancel) {
            return null;  // Room not found
        }

        roomToCancel.reserved = false;
        roomToCancel.guestName = undefined;

        return roomToCancel;

    }

    public listAvailableRooms(): Room[] {
        return this.rooms.filter(room => !room.reserved);
    }

    public getRoomDetails(roomNumber: number): Room | null {
        return this.rooms.find(room => room.roomNumber === roomNumber);

    }

    public listGuests(): string[] {
        return this.rooms.filter(room => room.reserved).map(room => room.guestName);
    }

    public changeRoomType(roomNumber: number, newType: RoomType): boolean {
        const roomToChange = this.rooms.find(room => room.roomNumber === roomNumber);
        if (!roomToChange) {
            return false;  // Room not found
        }

        roomToChange.type = newType;
        return true;
    }

    public getTotalRoomsByType(type: RoomType): number {
        return this.rooms.filter(room => room.type === type).length;
    }

    public getReservedRoomsByType(type: RoomType): number {
        return this.rooms.filter(room => room.type === type && room.reserved).length;
    }

}
