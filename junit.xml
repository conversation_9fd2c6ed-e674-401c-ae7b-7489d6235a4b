<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="7" failures="0" errors="0" time="1.46">
  <testsuite name="HotelBookingService" errors="0" failures="0" skipped="0" timestamp="2023-10-31T14:26:53" time="1.355" tests="7">
    <testcase classname="HotelBookingService should book a single room" name="HotelBookingService should book a single room" time="0.002">
    </testcase>
    <testcase classname="HotelBookingService should return null when there is no available room of desired type" name="HotelBookingService should return null when there is no available room of desired type" time="0">
    </testcase>
    <testcase classname="HotelBookingService should cancel a room booking and return the cancelled room as a result" name="HotelBookingService should cancel a room booking and return the cancelled room as a result" time="0">
    </testcase>
    <testcase classname="HotelBookingService should return null if trying to cancel a booking of a room that does not exist" name="HotelBookingService should return null if trying to cancel a booking of a room that does not exist" time="0">
    </testcase>
    <testcase classname="HotelBookingService should return all available rooms" name="HotelBookingService should return all available rooms" time="0">
    </testcase>
    <testcase classname="HotelBookingService should return room details of a given room number" name="HotelBookingService should return room details of a given room number" time="0">
    </testcase>
    <testcase classname="HotelBookingService should return null if room number does not exist" name="HotelBookingService should return null if room number does not exist" time="0.001">
    </testcase>
  </testsuite>
</testsuites>